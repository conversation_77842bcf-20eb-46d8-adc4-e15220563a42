/**
 * Chart Creation Dialog
 * Comprehensive chart creation interface with all required features
 */

/**
 * Show the chart creation dialog
 * @param {Object} selectedRange - The selected cell range
 */
export function showChartCreationDialog(selectedRange) {
    console.log('Opening chart creation dialog with range:', selectedRange);

    // Check if chart libraries are available
    const hasApex = typeof window.ApexCharts !== 'undefined';
    const hasECharts = typeof window.echarts !== 'undefined';
    const hasD3 = typeof window.d3 !== 'undefined';

    if (!hasApex && !hasECharts && !hasD3) {
        console.warn('No chart libraries detected. Charts will use basic visualization.');
    }

    // Extract data from the selected range
    const chartData = extractChartData(selectedRange);

    if (!chartData || !chartData.isValid) {
        alert('Invalid or insufficient data selected for chart creation.\nPlease select a range with at least 2 rows and 2 columns.');
        return;
    }

    // Create and show the dialog
    const dialog = new ChartCreationDialog(selectedRange, chartData);
    dialog.show();
}

/**
 * Extract data from selected range for chart creation
 * @param {Object} range - The selected range
 * @returns {Object} Extracted chart data
 */
function extractChartData(range) {
    if (!range) {
        return { isValid: false, error: 'No range selected' };
    }

    try {
        const startRow = range.start.r;
        const endRow = range.end.r;
        const startCol = range.start.c;
        const endCol = range.end.c;

        console.log(`Extracting data from range [${startRow},${startCol}] to [${endRow},${endCol}]`);

        const data = [];
        const categories = [];
        const series = [];

        // Try multiple methods to extract data from cells
        const extractedData = extractDataFromMultipleSources(startRow, endRow, startCol, endCol);

        if (!extractedData || extractedData.length === 0) {
            // Generate sample data if no data found
            console.warn('No data found in range, generating sample data');
            return generateSampleChartData();
        }

        // Use the extracted data
        for (let r = 0; r < extractedData.length; r++) {
            data.push(extractedData[r]);
        }

        if (data.length === 0) {
            console.warn('No data found, generating sample data');
            return generateSampleChartData();
        }

        // Determine if first row contains headers
        const hasHeaders = data.length > 1 && data[0].some(cell =>
            typeof cell === 'string' && cell.trim() !== '' && isNaN(parseFloat(cell))
        );

        // Determine if first column contains categories
        const hasCategories = data.some(row => row.length > 1 &&
            typeof row[0] === 'string' && row[0].trim() !== '' && isNaN(parseFloat(row[0]))
        );

        console.log('Data analysis:', { hasHeaders, hasCategories, dataRows: data.length, dataCols: data[0].length });

        // Extract categories and series
        const dataStartRow = hasHeaders ? 1 : 0;
        const dataStartCol = hasCategories ? 1 : 0;

        // Extract categories (from first column or generate default)
        if (hasCategories) {
            for (let r = dataStartRow; r < data.length; r++) {
                categories.push(data[r][0] || `Category ${r - dataStartRow + 1}`);
            }
        } else {
            for (let r = dataStartRow; r < data.length; r++) {
                categories.push(`Category ${r - dataStartRow + 1}`);
            }
        }

        // Extract series data
        const seriesNames = hasHeaders ? data[0].slice(dataStartCol) : [];
        const numSeries = Math.max(1, data[0].length - dataStartCol);

        for (let s = 0; s < numSeries; s++) {
            const seriesData = [];
            const seriesName = seriesNames[s] || `Series ${s + 1}`;

            for (let r = dataStartRow; r < data.length; r++) {
                const value = data[r][dataStartCol + s];
                const numValue = parseFloat(value);
                seriesData.push(isNaN(numValue) ? 0 : numValue);
            }

            series.push({
                name: seriesName,
                data: seriesData
            });
        }

        // For pie/doughnut charts, use first series only
        const pieData = {
            labels: categories,
            values: series[0] ? series[0].data : []
        };

        return {
            isValid: true,
            rawData: data,
            categories: categories,
            series: series,
            pieData: pieData,
            hasHeaders: hasHeaders,
            hasCategories: hasCategories,
            range: range
        };

    } catch (error) {
        console.error('Error extracting chart data:', error);
        // Return sample data instead of failing
        console.warn('Falling back to sample data due to error');
        return generateSampleChartData();
    }
}

/**
 * Extract data from multiple sources (different spreadsheet libraries)
 * @param {number} startRow - Start row
 * @param {number} endRow - End row
 * @param {number} startCol - Start column
 * @param {number} endCol - End column
 * @returns {Array} Extracted data array
 */
function extractDataFromMultipleSources(startRow, endRow, startCol, endCol) {
    const data = [];

    // Method 1: Try window.currentSheet (xlsx-populate)
    if (window.currentSheet && typeof window.currentSheet.cell === 'function') {
        try {
            console.log('Trying to extract data using window.currentSheet');
            for (let r = startRow; r <= endRow; r++) {
                const row = [];
                for (let c = startCol; c <= endCol; c++) {
                    try {
                        const cell = window.currentSheet.cell(r, c);
                        const value = cell ? cell.value() : '';
                        row.push(value || '');
                    } catch (error) {
                        console.warn(`Error reading cell [${r},${c}]:`, error);
                        row.push('');
                    }
                }
                data.push(row);
            }
            if (data.length > 0) {
                console.log('Successfully extracted data using window.currentSheet');
                return data;
            }
        } catch (error) {
            console.warn('Failed to extract data using window.currentSheet:', error);
        }
    }

    // Method 2: Try DOM table extraction
    try {
        console.log('Trying to extract data from DOM table');
        const table = document.querySelector('.excel-table');
        if (table) {
            for (let r = startRow; r <= endRow; r++) {
                const row = [];
                for (let c = startCol; c <= endCol; c++) {
                    const cell = table.querySelector(`td[data-row="${r}"][data-col="${c}"]`);
                    if (cell) {
                        const input = cell.querySelector('input');
                        const value = input ? input.value : cell.textContent;
                        row.push(value || '');
                    } else {
                        row.push('');
                    }
                }
                data.push(row);
            }
            if (data.length > 0) {
                console.log('Successfully extracted data from DOM table');
                return data;
            }
        }
    } catch (error) {
        console.warn('Failed to extract data from DOM table:', error);
    }

    // Method 3: Try workbook data
    if (window.workbook && window.workbook.activeSheet) {
        try {
            console.log('Trying to extract data using window.workbook');
            const sheet = window.workbook.activeSheet();
            if (sheet && sheet.cell) {
                for (let r = startRow; r <= endRow; r++) {
                    const row = [];
                    for (let c = startCol; c <= endCol; c++) {
                        try {
                            const cell = sheet.cell(r, c);
                            const value = cell ? cell.value() : '';
                            row.push(value || '');
                        } catch (error) {
                            row.push('');
                        }
                    }
                    data.push(row);
                }
                if (data.length > 0) {
                    console.log('Successfully extracted data using window.workbook');
                    return data;
                }
            }
        } catch (error) {
            console.warn('Failed to extract data using window.workbook:', error);
        }
    }

    console.warn('All data extraction methods failed');
    return [];
}

/**
 * Generate sample chart data when no real data is available
 * @returns {Object} Sample chart data
 */
function generateSampleChartData() {
    console.log('Generating sample chart data');
    return {
        isValid: true,
        rawData: [
            ['Category', 'Series 1', 'Series 2'],
            ['Q1', 10, 15],
            ['Q2', 20, 25],
            ['Q3', 15, 30],
            ['Q4', 25, 20]
        ],
        categories: ['Q1', 'Q2', 'Q3', 'Q4'],
        series: [
            { name: 'Series 1', data: [10, 20, 15, 25] },
            { name: 'Series 2', data: [15, 25, 30, 20] }
        ],
        pieData: {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            values: [10, 20, 15, 25]
        },
        hasHeaders: true,
        hasCategories: true,
        range: { start: { r: 0, c: 0 }, end: { r: 4, c: 2 } }
    };
}

/**
 * Chart Creation Dialog Class
 */
class ChartCreationDialog {
    constructor(selectedRange, chartData) {
        this.selectedRange = selectedRange;
        this.chartData = chartData;
        this.currentChartType = 'column';
        this.currentColorScheme = 'default';
        this.chartTitle = 'Chart Title';
        this.modal = null;
        this.previewChart = null;
        
        // Available chart types with SVG icons
        this.chartTypes = [
            { type: 'column', name: 'Column', icon: this.getChartIcon('column') },
            { type: 'bar', name: 'Bar', icon: this.getChartIcon('bar') },
            { type: 'line', name: 'Line', icon: this.getChartIcon('line') },
            { type: 'area', name: 'Area', icon: this.getChartIcon('area') },
            { type: 'pie', name: 'Pie', icon: this.getChartIcon('pie') },
            { type: 'doughnut', name: 'Doughnut', icon: this.getChartIcon('doughnut') },
            { type: 'scatter', name: 'Scatter', icon: this.getChartIcon('scatter') },
            { type: 'radar', name: 'Radar', icon: this.getChartIcon('radar') }
        ];
        
        // Color schemes
        this.colorSchemes = {
            default: ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0'],
            blue: ['#1f77b4', '#aec7e8', '#ff7f0e', '#ffbb78', '#2ca02c'],
            green: ['#2ca02c', '#98df8a', '#d62728', '#ff9896', '#9467bd'],
            red: ['#d62728', '#ff9896', '#2ca02c', '#98df8a', '#ff7f0e'],
            purple: ['#9467bd', '#c5b0d5', '#8c564b', '#c49c94', '#e377c2'],
            orange: ['#ff7f0e', '#ffbb78', '#2ca02c', '#98df8a', '#d62728']
        };
    }
    
    show() {
        this.createModal();
        this.setupEventListeners();
        this.updatePreview();
    }

    /**
     * Get SVG icon for chart type
     * @param {string} type - Chart type
     * @returns {string} SVG icon HTML
     */
    getChartIcon(type) {
        const icons = {
            column: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <rect x="3" y="12" width="4" height="9" fill="#5F6368"/>
                <rect x="10" y="8" width="4" height="13" fill="#5F6368"/>
                <rect x="17" y="4" width="4" height="17" fill="#5F6368"/>
            </svg>`,
            bar: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <rect x="3" y="3" width="9" height="4" fill="#5F6368"/>
                <rect x="3" y="10" width="13" height="4" fill="#5F6368"/>
                <rect x="3" y="17" width="17" height="4" fill="#5F6368"/>
            </svg>`,
            line: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <path d="M3 17L9 11L13 15L21 7" stroke="#5F6368" stroke-width="2" fill="none"/>
                <circle cx="3" cy="17" r="2" fill="#5F6368"/>
                <circle cx="9" cy="11" r="2" fill="#5F6368"/>
                <circle cx="13" cy="15" r="2" fill="#5F6368"/>
                <circle cx="21" cy="7" r="2" fill="#5F6368"/>
            </svg>`,
            area: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <path d="M3 17L9 11L13 15L21 7V21H3V17Z" fill="#5F6368" opacity="0.3"/>
                <path d="M3 17L9 11L13 15L21 7" stroke="#5F6368" stroke-width="2" fill="none"/>
            </svg>`,
            pie: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <circle cx="12" cy="12" r="9" fill="#5F6368" opacity="0.3"/>
                <path d="M12 3A9 9 0 0 1 21 12H12V3Z" fill="#5F6368"/>
            </svg>`,
            doughnut: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <circle cx="12" cy="12" r="9" fill="#5F6368" opacity="0.3"/>
                <circle cx="12" cy="12" r="4" fill="white"/>
                <path d="M12 3A9 9 0 0 1 21 12A9 9 0 0 1 12 21V16A4 4 0 0 0 16 12A4 4 0 0 0 12 8V3Z" fill="#5F6368"/>
            </svg>`,
            scatter: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <circle cx="6" cy="18" r="2" fill="#5F6368"/>
                <circle cx="10" cy="12" r="2" fill="#5F6368"/>
                <circle cx="14" cy="8" r="2" fill="#5F6368"/>
                <circle cx="18" cy="6" r="2" fill="#5F6368"/>
                <circle cx="8" cy="14" r="2" fill="#5F6368"/>
                <circle cx="16" cy="16" r="2" fill="#5F6368"/>
            </svg>`,
            radar: `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 32px; height: 32px;">
                <polygon points="12,2 20,8 20,16 12,22 4,16 4,8" stroke="#5F6368" stroke-width="1" fill="none"/>
                <polygon points="12,6 16,9 16,15 12,18 8,15 8,9" stroke="#5F6368" stroke-width="1" fill="none"/>
                <polygon points="12,8 15,10 15,14 12,16 9,14 9,10" fill="#5F6368" opacity="0.3"/>
                <line x1="12" y1="2" x2="12" y2="22" stroke="#5F6368" stroke-width="1"/>
                <line x1="4" y1="8" x2="20" y2="16" stroke="#5F6368" stroke-width="1"/>
                <line x1="4" y1="16" x2="20" y2="8" stroke="#5F6368" stroke-width="1"/>
            </svg>`
        };

        return icons[type] || icons.column;
    }
    
    createModal() {
        // Create modal overlay
        this.modal = document.createElement('div');
        this.modal.className = 'chart-creation-modal-overlay';
        this.modal.innerHTML = this.getModalHTML();
        
        // Add to document
        document.body.appendChild(this.modal);
        
        // Show modal with animation
        setTimeout(() => {
            this.modal.classList.add('show');
        }, 10);
    }
    
    getModalHTML() {
        return `
            <div class="chart-creation-modal">
                <div class="chart-modal-header">
                    <h2>Create Chart</h2>
                    <button class="chart-modal-close" title="Close">&times;</button>
                </div>
                
                <div class="chart-modal-content">
                    <div class="chart-modal-tabs">
                        <button class="chart-tab active" data-tab="type">Chart Type</button>
                        <button class="chart-tab" data-tab="data">Data</button>
                        <button class="chart-tab" data-tab="style">Style</button>
                        <button class="chart-tab" data-tab="preview">Preview</button>
                    </div>
                    
                    <div class="chart-modal-panels">
                        ${this.getChartTypePanel()}
                        ${this.getDataPanel()}
                        ${this.getStylePanel()}
                        ${this.getPreviewPanel()}
                    </div>
                </div>
                
                <div class="chart-modal-footer">
                    <button class="chart-btn chart-btn-secondary" id="chartCancelBtn">Cancel</button>
                    <button class="chart-btn chart-btn-primary" id="chartCreateBtn">Create Chart</button>
                </div>
            </div>
        `;
    }
    
    getChartTypePanel() {
        const typeOptions = this.chartTypes.map(type => `
            <div class="chart-type-option ${type.type === this.currentChartType ? 'selected' : ''}"
                 data-type="${type.type}">
                <div class="chart-type-icon">${type.icon}</div>
                <div class="chart-type-name">${type.name}</div>
            </div>
        `).join('');

        return `
            <div class="chart-panel active" data-panel="type">
                <h3>Select Chart Type</h3>
                <div class="chart-type-grid">
                    ${typeOptions}
                </div>
                <div class="chart-type-description">
                    <p id="chartTypeDescription">Select a chart type to see its description.</p>
                </div>
            </div>
        `;
    }
    
    getDataPanel() {
        const rangeText = this.formatRange(this.selectedRange);
        
        return `
            <div class="chart-panel" data-panel="data">
                <h3>Data Configuration</h3>
                <div class="data-config-section">
                    <label>Data Range:</label>
                    <div class="data-range-input">
                        <input type="text" id="dataRangeInput" value="${rangeText}" readonly>
                        <button id="selectRangeBtn" class="chart-btn chart-btn-small">Select Range</button>
                    </div>
                </div>
                
                <div class="data-config-section">
                    <label>Chart Title:</label>
                    <input type="text" id="chartTitleInput" value="${this.chartTitle}" placeholder="Enter chart title">
                </div>
                
                <div class="data-config-section">
                    <label>
                        <input type="checkbox" id="includeHeadersCheck" ${this.chartData.hasHeaders ? 'checked' : ''}>
                        First row contains headers
                    </label>
                </div>
                
                <div class="data-config-section">
                    <label>
                        <input type="checkbox" id="includeCategoriesCheck" ${this.chartData.hasCategories ? 'checked' : ''}>
                        First column contains categories
                    </label>
                </div>
                
                <div class="data-preview">
                    <h4>Data Preview:</h4>
                    <div class="data-preview-table">
                        ${this.generateDataPreviewTable()}
                    </div>
                </div>
            </div>
        `;
    }
    
    getStylePanel() {
        const colorOptions = Object.keys(this.colorSchemes).map(scheme => `
            <div class="color-scheme-option ${scheme === this.currentColorScheme ? 'selected' : ''}" 
                 data-scheme="${scheme}">
                <div class="color-scheme-preview">
                    ${this.colorSchemes[scheme].slice(0, 3).map(color => 
                        `<div class="color-dot" style="background-color: ${color}"></div>`
                    ).join('')}
                </div>
                <div class="color-scheme-name">${scheme.charAt(0).toUpperCase() + scheme.slice(1)}</div>
            </div>
        `).join('');
        
        return `
            <div class="chart-panel" data-panel="style">
                <h3>Chart Styling</h3>
                
                <div class="style-config-section">
                    <label>Color Scheme:</label>
                    <div class="color-scheme-grid">
                        ${colorOptions}
                    </div>
                </div>
                
                <div class="style-config-section">
                    <label>Chart Library:</label>
                    <select id="chartLibrarySelect">
                        <option value="auto">Auto (Best Available)</option>
                        <option value="apexcharts">ApexCharts</option>
                        <option value="echarts">ECharts</option>
                        <option value="d3">D3.js</option>
                    </select>
                </div>
                
                <div class="style-config-section">
                    <label>
                        <input type="checkbox" id="showLegendCheck" checked>
                        Show Legend
                    </label>
                </div>
                
                <div class="style-config-section">
                    <label>
                        <input type="checkbox" id="showGridCheck" checked>
                        Show Grid Lines
                    </label>
                </div>
                
                <div class="style-config-section">
                    <label>
                        <input type="checkbox" id="enableAnimationsCheck" checked>
                        Enable Animations
                    </label>
                </div>
            </div>
        `;
    }
    
    getPreviewPanel() {
        return `
            <div class="chart-panel" data-panel="preview">
                <h3>Chart Preview</h3>
                <div class="chart-preview-container">
                    <div id="chartPreviewArea">
                        <div class="chart-preview-placeholder">
                            <p>Select a chart type to see preview</p>
                        </div>
                    </div>
                </div>
                <div class="chart-preview-controls">
                    <button id="refreshPreviewBtn" class="chart-btn chart-btn-secondary">Refresh Preview</button>
                </div>
            </div>
        `;
    }

    generateDataPreviewTable() {
        const maxRows = Math.min(5, this.chartData.rawData.length);
        const maxCols = Math.min(5, this.chartData.rawData[0] ? this.chartData.rawData[0].length : 0);

        let html = '<table class="data-preview-table-inner">';

        for (let r = 0; r < maxRows; r++) {
            html += '<tr>';
            for (let c = 0; c < maxCols; c++) {
                const value = this.chartData.rawData[r] && this.chartData.rawData[r][c] !== undefined
                    ? this.chartData.rawData[r][c]
                    : '';
                const isHeader = r === 0 && this.chartData.hasHeaders;
                const isCategory = c === 0 && this.chartData.hasCategories;
                const cellClass = isHeader || isCategory ? 'header-cell' : 'data-cell';

                html += `<td class="${cellClass}">${value}</td>`;
            }
            html += '</tr>';
        }

        html += '</table>';

        if (this.chartData.rawData.length > maxRows || (this.chartData.rawData[0] && this.chartData.rawData[0].length > maxCols)) {
            html += '<p class="data-preview-note">Showing first 5x5 cells...</p>';
        }

        return html;
    }

    formatRange(range) {
        if (!range) return 'No range';

        const startCell = this.numberToColumnLetter(range.start.c) + range.start.r;
        const endCell = this.numberToColumnLetter(range.end.c) + range.end.r;

        return `${startCell}:${endCell}`;
    }

    numberToColumnLetter(num) {
        let result = '';
        while (num > 0) {
            num--;
            result = String.fromCharCode(65 + (num % 26)) + result;
            num = Math.floor(num / 26);
        }
        return result;
    }

    setupEventListeners() {
        // Close button
        this.modal.querySelector('.chart-modal-close').addEventListener('click', () => this.close());

        // Cancel button
        this.modal.querySelector('#chartCancelBtn').addEventListener('click', () => this.close());

        // Create button
        this.modal.querySelector('#chartCreateBtn').addEventListener('click', () => this.createChart());

        // Tab switching
        this.modal.querySelectorAll('.chart-tab').forEach(tab => {
            tab.addEventListener('click', () => this.switchTab(tab.dataset.tab));
        });

        // Chart type selection
        this.modal.querySelectorAll('.chart-type-option').forEach(option => {
            option.addEventListener('click', () => this.selectChartType(option.dataset.type));
        });

        // Color scheme selection
        this.modal.querySelectorAll('.color-scheme-option').forEach(option => {
            option.addEventListener('click', () => this.selectColorScheme(option.dataset.scheme));
        });

        // Chart title input
        this.modal.querySelector('#chartTitleInput').addEventListener('input', (e) => {
            this.chartTitle = e.target.value;
            this.updatePreview();
        });

        // Data configuration checkboxes
        this.modal.querySelector('#includeHeadersCheck').addEventListener('change', () => {
            this.updateDataConfiguration();
        });

        this.modal.querySelector('#includeCategoriesCheck').addEventListener('change', () => {
            this.updateDataConfiguration();
        });

        // Refresh preview button
        this.modal.querySelector('#refreshPreviewBtn').addEventListener('click', () => this.updatePreview());

        // Close on overlay click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.close();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal) {
                this.close();
            }
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        this.modal.querySelectorAll('.chart-tab').forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // Update panels
        this.modal.querySelectorAll('.chart-panel').forEach(panel => {
            panel.classList.toggle('active', panel.dataset.panel === tabName);
        });

        // Update preview if switching to preview tab
        if (tabName === 'preview') {
            this.updatePreview();
        }
    }

    selectChartType(type) {
        this.currentChartType = type;

        // Update UI
        this.modal.querySelectorAll('.chart-type-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.type === type);
        });

        // Update description
        this.updateChartTypeDescription(type);

        // Update preview
        this.updatePreview();
    }

    updateChartTypeDescription(type) {
        const descriptions = {
            column: 'Vertical bars showing data comparison across categories.',
            bar: 'Horizontal bars ideal for comparing values across categories.',
            line: 'Connected points showing trends over time or categories.',
            area: 'Line chart with filled area underneath, emphasizing magnitude.',
            pie: 'Circular chart showing proportional data as slices.',
            doughnut: 'Pie chart with a hollow center, modern appearance.',
            scatter: 'Points plotted to show correlation between two variables.',
            radar: 'Multi-axis chart comparing multiple variables simultaneously.'
        };

        const descElement = this.modal.querySelector('#chartTypeDescription');
        if (descElement) {
            descElement.textContent = descriptions[type] || 'Chart type description.';
        }
    }

    selectColorScheme(scheme) {
        this.currentColorScheme = scheme;

        // Update UI
        this.modal.querySelectorAll('.color-scheme-option').forEach(option => {
            option.classList.toggle('selected', option.dataset.scheme === scheme);
        });

        // Update preview
        this.updatePreview();
    }

    updateDataConfiguration() {
        const includeHeaders = this.modal.querySelector('#includeHeadersCheck').checked;
        const includeCategories = this.modal.querySelector('#includeCategoriesCheck').checked;

        // Re-extract data with new configuration
        this.chartData.hasHeaders = includeHeaders;
        this.chartData.hasCategories = includeCategories;

        // Re-process the data
        const processedData = this.reprocessData();
        this.chartData.categories = processedData.categories;
        this.chartData.series = processedData.series;
        this.chartData.pieData = processedData.pieData;

        // Update data preview
        const previewTable = this.modal.querySelector('.data-preview-table');
        if (previewTable) {
            previewTable.innerHTML = this.generateDataPreviewTable();
        }

        // Update preview
        this.updatePreview();
    }

    reprocessData() {
        const data = this.chartData.rawData;
        const categories = [];
        const series = [];

        const dataStartRow = this.chartData.hasHeaders ? 1 : 0;
        const dataStartCol = this.chartData.hasCategories ? 1 : 0;

        // Extract categories
        if (this.chartData.hasCategories) {
            for (let r = dataStartRow; r < data.length; r++) {
                categories.push(data[r][0] || `Category ${r - dataStartRow + 1}`);
            }
        } else {
            for (let r = dataStartRow; r < data.length; r++) {
                categories.push(`Category ${r - dataStartRow + 1}`);
            }
        }

        // Extract series
        const seriesNames = this.chartData.hasHeaders ? data[0].slice(dataStartCol) : [];
        const numSeries = Math.max(1, data[0].length - dataStartCol);

        for (let s = 0; s < numSeries; s++) {
            const seriesData = [];
            const seriesName = seriesNames[s] || `Series ${s + 1}`;

            for (let r = dataStartRow; r < data.length; r++) {
                const value = data[r][dataStartCol + s];
                const numValue = parseFloat(value);
                seriesData.push(isNaN(numValue) ? 0 : numValue);
            }

            series.push({
                name: seriesName,
                data: seriesData
            });
        }

        return {
            categories: categories,
            series: series,
            pieData: {
                labels: categories,
                values: series[0] ? series[0].data : []
            }
        };
    }

    async updatePreview() {
        const previewArea = this.modal.querySelector('#chartPreviewArea');
        if (!previewArea) return;

        // Show loading
        previewArea.innerHTML = '<div class="chart-loading">Generating preview...</div>';

        try {
            // Get current configuration
            const chartOptions = this.getChartOptions();

            // Create preview chart
            await this.createPreviewChart(previewArea, chartOptions);

        } catch (error) {
            console.error('Error updating preview:', error);
            previewArea.innerHTML = `
                <div class="chart-error">
                    <p>Preview Error</p>
                    <small>${error.message}</small>
                </div>
            `;
        }
    }

    getChartOptions() {
        return {
            type: this.currentChartType,
            title: this.chartTitle || 'Chart Title',
            colors: this.colorSchemes[this.currentColorScheme],
            showLegend: this.modal.querySelector('#showLegendCheck')?.checked ?? true,
            showGrid: this.modal.querySelector('#showGridCheck')?.checked ?? true,
            enableAnimations: this.modal.querySelector('#enableAnimationsCheck')?.checked ?? true,
            library: this.modal.querySelector('#chartLibrarySelect')?.value || 'auto'
        };
    }

    async createPreviewChart(container, options) {
        // Clear container and add rendering class
        container.innerHTML = '';
        container.className = 'chart-rendering';

        // Create chart container with proper dimensions
        const chartDiv = document.createElement('div');
        chartDiv.style.cssText = `
            width: 100%;
            height: 100%;
            min-width: 300px;
            min-height: 280px;
            position: relative;
            display: block;
            box-sizing: border-box;
        `;
        chartDiv.id = 'chart-preview-' + Date.now();
        container.appendChild(chartDiv);

        // Wait for DOM to be ready
        await new Promise(resolve => setTimeout(resolve, 150));

        // Ensure container has dimensions
        const containerRect = container.getBoundingClientRect();
        if (containerRect.width === 0 || containerRect.height === 0) {
            console.warn('Chart container has no dimensions, setting explicit size');
            chartDiv.style.width = '400px';
            chartDiv.style.height = '300px';
        } else {
            chartDiv.style.width = (containerRect.width - 20) + 'px';
            chartDiv.style.height = (containerRect.height - 20) + 'px';
        }

        // Prepare data based on chart type
        const chartData = this.prepareChartData(options.type);

        try {
            // Try to create chart with available libraries
            const success = await this.tryCreateChart(chartDiv, options.type, chartData, options);

            if (success) {
                container.className = 'chart-rendered';
                console.log('Chart preview created successfully');
            } else {
                throw new Error('All chart libraries failed');
            }
        } catch (error) {
            console.error('Chart preview creation failed:', error);
            container.className = '';
            container.innerHTML = `
                <div class="chart-error">
                    <p>Unable to create preview</p>
                    <small>${error.message || 'No chart libraries available'}</small>
                </div>
            `;
        }
    }

    prepareChartData(chartType) {
        console.log('Preparing chart data for type:', chartType);
        console.log('Available chart data:', this.chartData);

        if (chartType === 'pie' || chartType === 'doughnut') {
            const pieData = this.chartData.pieData || {};

            // Ensure we have valid pie data
            if (!pieData.labels || !pieData.values || pieData.labels.length === 0) {
                console.warn('Invalid pie data, generating sample data');
                return {
                    labels: ['Sample A', 'Sample B', 'Sample C'],
                    values: [30, 40, 30]
                };
            }

            console.log('Using pie data:', pieData);
            return pieData;
        } else {
            const categories = this.chartData.categories || [];
            const series = this.chartData.series || [];

            // Ensure we have valid series data
            if (categories.length === 0 || series.length === 0) {
                console.warn('Invalid series data, generating sample data');
                return {
                    categories: ['Category 1', 'Category 2', 'Category 3'],
                    series: [{
                        name: 'Sample Series',
                        data: [10, 20, 15]
                    }]
                };
            }

            console.log('Using series data:', { categories, series });
            return {
                categories: categories,
                series: series
            };
        }
    }

    async tryCreateChart(container, type, data, options) {
        console.log('Attempting to create chart:', { type, library: options.library, data });

        // Check available libraries
        const hasApex = typeof window.ApexCharts !== 'undefined';
        const hasECharts = typeof window.echarts !== 'undefined';
        const hasD3 = typeof window.d3 !== 'undefined';

        console.log('Available libraries:', { hasApex, hasECharts, hasD3 });

        // Try ApexCharts first
        if (hasApex && (options.library === 'auto' || options.library === 'apexcharts')) {
            try {
                console.log('Trying ApexCharts...');
                await this.createApexChart(container, type, data, options);
                console.log('ApexCharts success');
                return true;
            } catch (error) {
                console.warn('ApexCharts failed:', error);
            }
        }

        // Try ECharts
        if (hasECharts && (options.library === 'auto' || options.library === 'echarts')) {
            try {
                console.log('Trying ECharts...');
                await this.createEChart(container, type, data, options);
                console.log('ECharts success');
                return true;
            } catch (error) {
                console.warn('ECharts failed:', error);
            }
        }

        // Try D3.js
        if (hasD3 && (options.library === 'auto' || options.library === 'd3')) {
            try {
                console.log('Trying D3.js...');
                await this.createD3Chart(container, type, data, options);
                console.log('D3.js success');
                return true;
            } catch (error) {
                console.warn('D3.js failed:', error);
            }
        }

        // Fallback to basic chart
        try {
            console.log('Trying basic chart fallback...');
            this.createBasicChart(container, type, data, options);
            console.log('Basic chart success');
            return true;
        } catch (error) {
            console.error('All chart creation methods failed:', error);
            return false;
        }
    }

    /**
     * Create a basic chart using HTML/CSS when libraries fail
     * @param {HTMLElement} container - Container element
     * @param {string} type - Chart type
     * @param {Object} data - Chart data
     * @param {Object} options - Chart options
     */
    createBasicChart(container, type, data, options) {
        container.innerHTML = '';

        const chartDiv = document.createElement('div');
        chartDiv.style.cssText = `
            width: 100%;
            height: 100%;
            padding: 20px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 4px;
        `;

        // Add title
        const title = document.createElement('h3');
        title.textContent = options.title || 'Chart';
        title.style.cssText = `
            margin: 0 0 20px 0;
            color: #333;
            font-size: 16px;
            text-align: center;
        `;
        chartDiv.appendChild(title);

        if (type === 'pie' || type === 'doughnut') {
            this.createBasicPieChart(chartDiv, data, options);
        } else {
            this.createBasicBarChart(chartDiv, data, options);
        }

        container.appendChild(chartDiv);
    }

    /**
     * Create a basic pie chart using HTML/CSS
     */
    createBasicPieChart(container, data, options) {
        const pieContainer = document.createElement('div');
        pieContainer.style.cssText = `
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: conic-gradient(
                #008FFB 0deg 120deg,
                #00E396 120deg 240deg,
                #FEB019 240deg 360deg
            );
            margin: 20px auto;
        `;

        const legend = document.createElement('div');
        legend.style.cssText = `
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        `;

        const colors = ['#008FFB', '#00E396', '#FEB019'];
        const labels = data.labels || ['Series 1', 'Series 2', 'Series 3'];

        labels.forEach((label, index) => {
            const legendItem = document.createElement('div');
            legendItem.style.cssText = `
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 12px;
            `;
            legendItem.innerHTML = `
                <div style="width: 12px; height: 12px; background: ${colors[index % colors.length]}; border-radius: 2px;"></div>
                <span>${label}</span>
            `;
            legend.appendChild(legendItem);
        });

        container.appendChild(pieContainer);
        container.appendChild(legend);
    }

    /**
     * Create a basic bar chart using HTML/CSS
     */
    createBasicBarChart(container, data, options) {
        const chartContainer = document.createElement('div');
        chartContainer.style.cssText = `
            width: 100%;
            max-width: 400px;
            height: 200px;
            display: flex;
            align-items: end;
            justify-content: space-around;
            border-bottom: 2px solid #ddd;
            border-left: 2px solid #ddd;
            padding: 20px;
            box-sizing: border-box;
        `;

        const categories = data.categories || ['Cat 1', 'Cat 2', 'Cat 3'];
        const values = data.series?.[0]?.data || [10, 20, 15];
        const maxValue = Math.max(...values);

        categories.forEach((category, index) => {
            const barContainer = document.createElement('div');
            barContainer.style.cssText = `
                display: flex;
                flex-direction: column;
                align-items: center;
                height: 100%;
                justify-content: end;
            `;

            const bar = document.createElement('div');
            const height = (values[index] / maxValue) * 150;
            bar.style.cssText = `
                width: 30px;
                height: ${height}px;
                background: #008FFB;
                margin-bottom: 5px;
                border-radius: 2px 2px 0 0;
            `;

            const label = document.createElement('div');
            label.textContent = category;
            label.style.cssText = `
                font-size: 10px;
                color: #666;
                text-align: center;
                max-width: 40px;
                word-wrap: break-word;
            `;

            barContainer.appendChild(bar);
            barContainer.appendChild(label);
            chartContainer.appendChild(barContainer);
        });

        container.appendChild(chartContainer);
    }

    async createApexChart(container, type, data, options) {
        // Ensure container has an ID for ApexCharts
        if (!container.id) {
            container.id = 'apex-chart-' + Date.now();
        }

        // Verify container is in DOM and has dimensions
        if (!container.parentNode) {
            throw new Error('Container not in DOM for ApexCharts');
        }

        // Wait for container to have dimensions
        let attempts = 0;
        while ((container.clientWidth === 0 || container.clientHeight === 0) && attempts < 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (container.clientWidth === 0 || container.clientHeight === 0) {
            console.warn('Container still has no dimensions, using fallback sizes');
            container.style.width = '400px';
            container.style.height = '300px';
        }

        const chartOptions = {
            chart: {
                type: this.mapChartType(type, 'apex'),
                height: container.clientHeight || 300,
                width: container.clientWidth || 400,
                animations: {
                    enabled: options.enableAnimations !== false
                },
                toolbar: {
                    show: false
                },
                background: 'transparent'
            },
            title: {
                text: options.title || 'Chart',
                align: 'center',
                style: {
                    fontSize: '16px',
                    color: '#333'
                }
            },
            colors: options.colors || ['#008FFB', '#00E396', '#FEB019', '#FF4560', '#775DD0'],
            legend: {
                show: options.showLegend !== false,
                position: 'bottom'
            },
            grid: {
                show: options.showGrid !== false
            },
            dataLabels: {
                enabled: false
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 300
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        if (type === 'pie' || type === 'doughnut') {
            chartOptions.labels = data.labels || ['No Data'];
            chartOptions.series = data.values || [1];
            if (type === 'doughnut') {
                chartOptions.plotOptions = {
                    pie: {
                        donut: {
                            size: '60%'
                        }
                    }
                };
            }
        } else {
            chartOptions.xaxis = {
                categories: data.categories || ['No Data']
            };
            chartOptions.series = data.series || [{ name: 'No Data', data: [0] }];
        }

        // Clean up previous chart
        if (this.previewChart && this.previewChart.destroy) {
            try {
                this.previewChart.destroy();
                this.previewChart = null;
            } catch (e) {
                console.warn('Error destroying previous chart:', e);
            }
        }

        try {
            // Create new chart
            console.log('Creating ApexChart with options:', chartOptions);
            const chart = new ApexCharts(container, chartOptions);

            // Render chart
            await chart.render();

            // Store chart instance for cleanup
            this.previewChart = chart;

            console.log('ApexChart rendered successfully');

        } catch (error) {
            console.error('Error creating ApexChart:', error);
            throw new Error(`ApexChart creation failed: ${error.message}`);
        }
    }

    /**
     * Map chart type to library-specific type
     * @param {string} type - Generic chart type
     * @param {string} library - Chart library ('apex', 'echarts', 'd3')
     * @returns {string} Library-specific chart type
     */
    mapChartType(type, library) {
        const typeMap = {
            apex: {
                column: 'bar',
                bar: 'bar',
                line: 'line',
                area: 'area',
                pie: 'pie',
                doughnut: 'donut',
                scatter: 'scatter',
                radar: 'radar'
            },
            echarts: {
                column: 'bar',
                bar: 'bar',
                line: 'line',
                area: 'line',
                pie: 'pie',
                doughnut: 'pie',
                scatter: 'scatter',
                radar: 'radar'
            },
            d3: {
                column: 'bar',
                bar: 'bar',
                line: 'line',
                area: 'area',
                pie: 'pie',
                doughnut: 'pie',
                scatter: 'scatter',
                radar: 'radar'
            }
        };

        return typeMap[library]?.[type] || type;
    }

    /**
     * Create a chart container element for the spreadsheet
     * @param {Object} options - Chart options
     * @returns {HTMLElement} Chart container element
     */
    createChartContainer(options) {
        const container = document.createElement('div');
        container.className = 'chart-container';
        container.style.cssText = `
            position: absolute;
            top: 50px;
            left: 50px;
            width: 500px;
            height: 350px;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            resize: both;
            overflow: hidden;
            cursor: move;
        `;

        // Add chart header
        const header = document.createElement('div');
        header.className = 'chart-header';
        header.style.cssText = `
            background: #007bff;
            color: white;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;
        header.innerHTML = `
            <span>${options.title || 'Chart'}</span>
            <button class="chart-close-btn" style="
                background: none;
                border: none;
                color: white;
                font-size: 16px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">&times;</button>
        `;

        // Add chart content area
        const content = document.createElement('div');
        content.className = 'chart-content';
        content.style.cssText = `
            width: 100%;
            height: calc(100% - 40px);
            padding: 10px;
            box-sizing: border-box;
            background: white;
        `;

        container.appendChild(header);
        container.appendChild(content);

        // Add close functionality
        const closeBtn = header.querySelector('.chart-close-btn');
        closeBtn.addEventListener('click', () => {
            container.remove();
        });

        return container;
    }

    async createEChart(container, type, data, options) {
        // Ensure container has dimensions
        if (container.clientWidth === 0 || container.clientHeight === 0) {
            container.style.width = '400px';
            container.style.height = '300px';
        }

        // Wait for container to be ready
        await new Promise(resolve => setTimeout(resolve, 50));

        // Clean up previous chart
        if (this.previewChart && this.previewChart.dispose) {
            try {
                this.previewChart.dispose();
            } catch (e) {
                console.warn('Error disposing previous chart:', e);
            }
        }

        // Initialize ECharts
        const chart = echarts.init(container);

        const chartOptions = {
            title: {
                text: options.title,
                left: 'center',
                textStyle: {
                    fontSize: 16
                }
            },
            color: options.colors,
            legend: {
                show: options.showLegend,
                bottom: 10
            },
            grid: {
                show: options.showGrid,
                left: '10%',
                right: '10%',
                bottom: '15%',
                top: '20%'
            },
            tooltip: {
                trigger: 'item'
            }
        };

        if (type === 'pie' || type === 'doughnut') {
            chartOptions.series = [{
                type: 'pie',
                radius: type === 'doughnut' ? ['40%', '70%'] : '70%',
                center: ['50%', '50%'],
                data: (data.labels || []).map((label, index) => ({
                    name: label,
                    value: (data.values || [])[index] || 0
                })),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }];
        } else {
            chartOptions.xAxis = {
                type: 'category',
                data: data.categories || []
            };
            chartOptions.yAxis = {
                type: 'value'
            };
            chartOptions.series = (data.series || []).map(serie => ({
                name: serie.name,
                type: this.mapChartType(type, 'echarts'),
                data: serie.data || [],
                areaStyle: type === 'area' ? {} : undefined,
                smooth: type === 'line' || type === 'area'
            }));
            chartOptions.tooltip.trigger = 'axis';
        }

        chart.setOption(chartOptions);

        // Handle resize
        const resizeObserver = new ResizeObserver(() => {
            chart.resize();
        });
        resizeObserver.observe(container);

        // Store chart instance for cleanup
        this.previewChart = {
            dispose: () => {
                resizeObserver.disconnect();
                chart.dispose();
            },
            resize: () => chart.resize()
        };
    }

    async createD3Chart(container, type, data, options) {
        // Basic D3 implementation
        const svg = d3.select(container)
            .append('svg')
            .attr('width', '100%')
            .attr('height', '100%');

        // Add title
        svg.append('text')
            .attr('x', '50%')
            .attr('y', 30)
            .attr('text-anchor', 'middle')
            .style('font-size', '16px')
            .style('font-weight', 'bold')
            .text(options.title);

        // Simple bar chart implementation
        if (type === 'column' || type === 'bar') {
            const margin = { top: 50, right: 30, bottom: 40, left: 40 };
            const width = container.clientWidth - margin.left - margin.right;
            const height = 250 - margin.top - margin.bottom;

            const g = svg.append('g')
                .attr('transform', `translate(${margin.left},${margin.top})`);

            const x = d3.scaleBand()
                .domain(data.categories)
                .range([0, width])
                .padding(0.1);

            const y = d3.scaleLinear()
                .domain([0, d3.max(data.series[0].data)])
                .range([height, 0]);

            g.selectAll('.bar')
                .data(data.series[0].data)
                .enter().append('rect')
                .attr('class', 'bar')
                .attr('x', (d, i) => x(data.categories[i]))
                .attr('y', d => y(d))
                .attr('width', x.bandwidth())
                .attr('height', d => height - y(d))
                .attr('fill', options.colors[0]);

            g.append('g')
                .attr('transform', `translate(0,${height})`)
                .call(d3.axisBottom(x));

            g.append('g')
                .call(d3.axisLeft(y));
        }

        // Store for cleanup
        this.previewChart = { destroy: () => svg.remove() };
    }

    createBasicChart(container, type, data, options) {
        // Create a simple visual representation
        const categories = data.categories || data.labels || [];
        const values = data.values || (data.series && data.series[0] ? data.series[0].data : []);

        let chartHTML = `
            <div style="padding: 20px; text-align: center; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
                <h3 style="margin: 0 0 15px 0; color: #333;">${options.title}</h3>
                <p style="margin: 0 0 15px 0; color: #666;">Basic ${type} chart visualization</p>
        `;

        if (type === 'pie' || type === 'doughnut') {
            // Simple pie chart representation
            chartHTML += `<div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px;">`;
            categories.forEach((cat, i) => {
                const value = values[i] || 0;
                const color = options.colors[i % options.colors.length];
                chartHTML += `
                    <div style="display: flex; align-items: center; margin: 5px;">
                        <div style="width: 16px; height: 16px; background: ${color}; margin-right: 8px; border-radius: 50%;"></div>
                        <span style="font-size: 12px;">${cat}: ${value}</span>
                    </div>
                `;
            });
            chartHTML += `</div>`;
        } else {
            // Simple bar chart representation
            const maxValue = Math.max(...values);
            chartHTML += `<div style="display: flex; align-items: end; justify-content: center; height: 150px; gap: 5px; margin: 20px 0;">`;
            categories.forEach((cat, i) => {
                const value = values[i] || 0;
                const height = maxValue > 0 ? (value / maxValue) * 120 : 10;
                const color = options.colors[i % options.colors.length];
                chartHTML += `
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <div style="width: 30px; height: ${height}px; background: ${color}; margin-bottom: 5px; border-radius: 2px;"></div>
                        <span style="font-size: 10px; writing-mode: vertical-rl; text-orientation: mixed;">${cat}</span>
                    </div>
                `;
            });
            chartHTML += `</div>`;
        }

        chartHTML += `
                <div style="margin-top: 15px; font-size: 12px; color: #888;">
                    Chart libraries not available - showing basic visualization
                </div>
            </div>
        `;

        container.innerHTML = chartHTML;
        this.previewChart = { destroy: () => {} };
    }



    async createChart() {
        try {
            console.log('Creating final chart...');

            // Get final chart configuration
            const chartOptions = this.getChartOptions();
            const chartData = this.prepareChartData(chartOptions.type);

            console.log('Chart options:', chartOptions);
            console.log('Chart data:', chartData);

            // Create the chart container in the spreadsheet
            const chartContainer = this.createChartContainer(chartOptions);

            // Find the best container for the chart - try multiple selectors
            let spreadsheetContainer = document.querySelector('#spreadsheetContainer');
            if (!spreadsheetContainer) {
                spreadsheetContainer = document.querySelector('.excel-table-container');
            }
            if (!spreadsheetContainer) {
                spreadsheetContainer = document.querySelector('#excelContainer');
            }
            if (!spreadsheetContainer) {
                spreadsheetContainer = document.querySelector('.spreadsheet-container');
            }
            if (!spreadsheetContainer) {
                spreadsheetContainer = document.body;
                console.warn('Using document.body as fallback container');
            }
            if (!spreadsheetContainer) {
                spreadsheetContainer = document.querySelector('main');
            }
            if (!spreadsheetContainer) {
                spreadsheetContainer = document.body;
            }

            console.log('Chart container will be added to:', spreadsheetContainer.id || spreadsheetContainer.className || 'body');

            // Add to spreadsheet first so it has proper dimensions
            spreadsheetContainer.appendChild(chartContainer);

            // Force immediate layout calculation
            chartContainer.offsetHeight;
            await new Promise(resolve => setTimeout(resolve, 100));

            // Get the chart content area
            const chartContentArea = chartContainer.querySelector('.chart-content');

            // Ensure chart content area has proper dimensions
            const containerRect = chartContainer.getBoundingClientRect();
            const contentWidth = Math.max(460, containerRect.width - 20);
            const contentHeight = Math.max(330, containerRect.height - 60); // Account for header

            chartContentArea.style.width = `${contentWidth}px`;
            chartContentArea.style.height = `${contentHeight}px`;
            chartContentArea.style.minWidth = `${contentWidth}px`;
            chartContentArea.style.minHeight = `${contentHeight}px`;

            // Force layout recalculation
            chartContentArea.offsetHeight;
            await new Promise(resolve => setTimeout(resolve, 50));

            console.log('Chart content area dimensions:', {
                container: { width: containerRect.width, height: containerRect.height },
                content: { width: chartContentArea.clientWidth, height: chartContentArea.clientHeight },
                calculated: { width: contentWidth, height: contentHeight }
            });

            // Create the actual chart
            const success = await this.tryCreateChart(chartContentArea, chartOptions.type, chartData, chartOptions);

            if (!success) {
                // Remove container if chart creation failed
                chartContainer.remove();
                throw new Error('Failed to create chart with any available library');
            }

            // Make draggable and resizable
            this.makeChartInteractive(chartContainer);

            // Close dialog
            this.close();

            // Show success message
            if (window.updateStatus) {
                window.updateStatus('Chart created successfully', 'success');
            }

        } catch (error) {
            console.error('Error creating chart:', error);
            alert('Error creating chart: ' + error.message);
        }
    }

    createChartContainer(options) {
        const container = document.createElement('div');
        container.className = 'chart-container';
        container.style.cssText = `
            position: absolute;
            width: 500px;
            height: 400px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            top: 100px;
            left: 100px;
            z-index: 1000;
            overflow: hidden;
            resize: both;
            min-width: 300px;
            min-height: 200px;
            box-sizing: border-box;
        `;

        container.innerHTML = `
            <div class="chart-header" style="
                background: #f8f9fa;
                border-bottom: 1px solid #ddd;
                padding: 8px 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
                user-select: none;
                height: 40px;
                box-sizing: border-box;
            ">
                <span class="chart-title" style="font-weight: 600; color: #333;">${options.title}</span>
                <div class="chart-controls">
                    <button class="chart-btn-small edit-chart" title="Edit Chart" style="
                        background: none;
                        border: none;
                        padding: 6px 8px;
                        cursor: pointer;
                        border-radius: 4px;
                        margin-right: 4px;
                        color: #666;
                        transition: all 0.2s ease;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    "><span class="material-icons" style="font-size: 16px;">edit</span></button>
                    <button class="chart-btn-small delete-chart" title="Delete Chart" style="
                        background: none;
                        border: none;
                        padding: 6px 8px;
                        cursor: pointer;
                        border-radius: 4px;
                        color: #666;
                        transition: all 0.2s ease;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    "><span class="material-icons" style="font-size: 16px;">delete</span></button>
                </div>
            </div>
            <div class="chart-content" style="
                padding: 10px;
                height: calc(100% - 40px);
                overflow: hidden;
                box-sizing: border-box;
                position: relative;
            "></div>
        `;

        // Store chart configuration
        container.dataset.chartConfig = JSON.stringify({
            type: options.type,
            title: options.title,
            range: this.selectedRange,
            colors: options.colors,
            library: options.library
        });

        return container;
    }

    makeChartInteractive(container) {
        const header = container.querySelector('.chart-header');

        // Make draggable
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        header.addEventListener('mousedown', (e) => {
            if (e.target.closest('.chart-controls')) return; // Don't drag when clicking controls

            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(container.style.left) || 0;
            startTop = parseInt(container.style.top) || 0;

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
            e.preventDefault();
        });

        function onMouseMove(e) {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            container.style.left = (startLeft + deltaX) + 'px';
            container.style.top = (startTop + deltaY) + 'px';
        }

        function onMouseUp() {
            isDragging = false;
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }

        // Add control button handlers
        container.querySelector('.edit-chart').addEventListener('click', () => {
            // Re-open chart dialog with current settings
            const config = JSON.parse(container.dataset.chartConfig);
            const dialog = new ChartCreationDialog(config.range, this.chartData);
            dialog.currentChartType = config.type;
            dialog.chartTitle = config.title;
            dialog.show();

            // Remove current chart
            container.remove();
        });

        container.querySelector('.delete-chart').addEventListener('click', () => {
            if (confirm('Delete this chart?')) {
                container.remove();
                if (window.updateStatus) {
                    window.updateStatus('Chart deleted', 'info');
                }
            }
        });
    }

    close() {
        if (this.previewChart && this.previewChart.destroy) {
            this.previewChart.destroy();
        }

        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                if (this.modal && this.modal.parentNode) {
                    this.modal.parentNode.removeChild(this.modal);
                }
            }, 300);
        }
    }
}

// Make the function and class available globally
window.showChartCreationDialog = showChartCreationDialog;
window.ChartCreationDialog = ChartCreationDialog;

// Ensure the function is available when the module loads
if (typeof window !== 'undefined') {
    window.showChartCreationDialog = showChartCreationDialog;
}

// Test function for console debugging
window.testChartSystem = function() {
    console.log('Testing chart system...');

    // Test with sample range
    const sampleRange = {
        start: { r: 1, c: 1 },
        end: { r: 4, c: 3 }
    };

    try {
        showChartCreationDialog(sampleRange);
        console.log('Chart dialog opened successfully');
    } catch (error) {
        console.error('Chart dialog test failed:', error);
    }
};

// Show test panel function
window.showChartTestPanel = function() {
    const panel = document.getElementById('chartTestPanel');
    if (panel) {
        panel.style.display = 'block';
        console.log('Chart test panel shown. Click "Test Chart Dialog" to test the chart system.');
    } else {
        console.log('Chart test panel not found. Use window.testChartSystem() directly.');
    }
};

// Check chart libraries status
window.checkChartLibraries = function() {
    console.log('Chart Libraries Status:');
    console.log('- ApexCharts:', typeof window.ApexCharts !== 'undefined' ? '✓ Available' : '✗ Not Available');
    console.log('- ECharts:', typeof window.echarts !== 'undefined' ? '✓ Available' : '✗ Not Available');
    console.log('- D3.js:', typeof window.d3 !== 'undefined' ? '✓ Available' : '✗ Not Available');

    if (typeof window.ApexCharts === 'undefined' && typeof window.echarts === 'undefined' && typeof window.d3 === 'undefined') {
        console.warn('No chart libraries detected! Charts will use basic fallback rendering.');
    }
};

console.log('Chart creation dialog module loaded.');
console.log('Available test functions:');
console.log('- window.testChartSystem() - Test chart dialog');
console.log('- window.showChartTestPanel() - Show test panel');
console.log('- window.checkChartLibraries() - Check library status');
