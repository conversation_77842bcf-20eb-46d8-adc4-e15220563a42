# Chart System Testing Guide

## Quick Test Instructions

### 1. Open the Application
- Navigate to `http://localhost:8000/main.html` in your browser
- Open browser console (F12)

### 2. Test Chart Libraries
```javascript
// Check if chart libraries are loaded
window.checkChartLibraries()
```

### 3. Test Chart Dialog
```javascript
// Show test panel
window.showChartTestPanel()

// Or test directly
window.testChartSystem()
```

### 4. Test Chart Creation Flow

#### Method 1: Using the Chart Button
1. Click "New Workbook" or open an Excel file
2. Select a range of cells (or skip this step - it will use sample data)
3. Click the "Chart" button in the toolbar
4. Chart creation dialog should open

#### Method 2: Using Console Commands
```javascript
// Test with sample data
window.testChartSystem()
```

### 5. Test Chart Types
In the chart dialog:
1. **Chart Type Tab**: Select different chart types (Column, Bar, Line, Area, Pie, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Radar)
2. **Data Tab**: Verify data preview shows correctly
3. **Style Tab**: Try different color schemes and options
4. **Preview Tab**: Check if chart preview renders correctly

### 6. Test Chart Creation
1. Configure your chart settings
2. Click "Create Chart"
3. Chart should appear as a draggable/resizable container in the spreadsheet
4. Test drag functionality by dragging the chart header
5. Test edit/delete buttons in chart header

## Expected Behavior

### ✅ Working Features
- Chart dialog opens without errors
- All chart types are selectable
- Data preview shows sample data when no real data available
- Chart preview renders (using ApexCharts, ECharts, or basic fallback)
- Charts can be created and placed in spreadsheet
- Charts are draggable and resizable
- Chart editing and deletion works

### 🔧 Fallback Behavior
- If no chart libraries are available, basic HTML/CSS charts are used
- If no data is selected, sample data is automatically generated
- If no workbook is open, chart creation still works with sample data

## Troubleshooting

### Chart Dialog Doesn't Open
```javascript
// Check if function is available
console.log(typeof window.showChartCreationDialog)

// Check for errors
window.testChartSystem()
```

### Chart Libraries Not Loading
```javascript
// Check library status
window.checkChartLibraries()

// Manually check
console.log('ApexCharts:', typeof ApexCharts)
console.log('ECharts:', typeof echarts)
console.log('D3:', typeof d3)
```

### Chart Preview Not Showing
- Check browser console for errors
- Verify chart container has proper dimensions
- Try different chart types
- Check if fallback basic charts work

### Charts Not Creating in Spreadsheet
- Check if spreadsheet container exists
- Verify no JavaScript errors in console
- Try with different chart configurations

## Console Debug Commands

```javascript
// Show all available test functions
console.log('Available functions:', Object.keys(window).filter(k => k.includes('Chart') || k.includes('test')))

// Test specific chart type
const range = { start: { r: 1, c: 1 }, end: { r: 4, c: 3 } }
window.showChartCreationDialog(range)

// Check if chart creation dialog class is available
console.log('ChartCreationDialog:', typeof window.ChartCreationDialog)
```

## Known Issues Fixed

1. ✅ Modal not displaying properly
2. ✅ Chart libraries not being detected
3. ✅ Data extraction failing when no workbook open
4. ✅ Chart preview not rendering
5. ✅ Charts not being created in spreadsheet
6. ✅ Missing error handling
7. ✅ Selection manager integration issues
8. ✅ Chart type mapping problems
9. ✅ Container dimension issues
10. ✅ Missing fallback mechanisms

## Performance Notes

- Chart creation should complete within 2-3 seconds
- Preview updates should be near-instantaneous
- Large datasets (>100 data points) may take longer to render
- Basic fallback charts render faster than library-based charts
